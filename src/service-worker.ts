// Chrome 扩展的 Service Worker
import { tabManager } from './background/TabManager';

// Service Worker启动日志
console.log('=== OneTab Plus Service Worker 启动 ===');
console.log('版本:', chrome.runtime.getManifest().version);
console.log('启动时间:', new Date().toISOString());
console.log('Chrome APIs 可用性检查:');
console.log('- chrome.tabs:', !!chrome.tabs);
console.log('- chrome.runtime:', !!chrome.runtime);
console.log('- chrome.action:', !!chrome.action);
console.log('- chrome.storage:', !!chrome.storage);
console.log('=====================================');

// 移除定时同步功能，简化逻辑
console.log('Service Worker: 已简化同步逻辑，只保留手动同步功能');

// 初始安装或更新时
chrome.runtime.onInstalled.addListener(() => {
  console.log('Service Worker: 扩展已安装或更新');

  // 创建右键菜单
  chrome.contextMenus.create({
    id: 'open-tab-manager',
    title: '打开标签管理器',
    contexts: ['action']
  });

  chrome.contextMenus.create({
    id: 'saveCurrentTab',
    title: '保存当前标签',
    contexts: ['action']
  });
});

// 浏览器启动时
chrome.runtime.onStartup.addListener(() => {
  console.log('Service Worker: 浏览器已启动');
});

// Service Worker就绪检查函数
async function ensureServiceWorkerReady(): Promise<void> {
  // 检查Chrome APIs是否可用
  if (!chrome.tabs || !chrome.runtime) {
    throw new Error('Chrome APIs not available');
  }

  // 简单的权限测试
  try {
    await chrome.tabs.query({ active: true, currentWindow: true });
  } catch (error) {
    throw new Error('Tabs permission not ready');
  }
}

// 带重试机制的标签查询
async function queryTabsWithRetry(maxRetries = 3): Promise<chrome.tabs.Tab[]> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await ensureServiceWorkerReady();
      return await chrome.tabs.query({ currentWindow: true });
    } catch (error) {
      console.warn(`标签查询失败，重试 ${i + 1}/${maxRetries}:`, error);
      if (i === maxRetries - 1) throw error;
      // 指数退避重试
      await new Promise(resolve => setTimeout(resolve, 100 * Math.pow(2, i)));
    }
  }
  return [];
}

// 监听扩展图标点击事件
chrome.action.onClicked.addListener(async () => {
  console.log('Service Worker版本:', chrome.runtime.getManifest().version);
  console.log('点击扩展图标，开始处理标签收集');

  try {
    // 显示处理中的通知
    chrome.notifications.create({
      type: 'basic',
      iconUrl: '/icons/icon128.png',
      title: 'OneTab Plus',
      message: '正在收集标签页...'
    });

    // 使用重试机制查询标签
    console.log('开始查询标签页...');
    const tabs = await queryTabsWithRetry();
    console.log(`成功查询到 ${tabs.length} 个标签页`);

    // 先保存标签页
    console.log('开始保存标签页...');
    await tabManager.saveAllTabs(tabs);
    console.log('标签页保存完成');

    // 然后打开标签管理器
    console.log('打开标签管理器...');
    await tabManager.openTabManager(true);
    console.log('标签管理器已打开');

  } catch (error) {
    console.error('处理扩展图标点击失败:', error);

    // 显示用户友好的错误提示
    chrome.notifications.create({
      type: 'basic',
      iconUrl: '/icons/icon128.png',
      title: 'OneTab Plus - 操作失败',
      message: '无法收集标签页，请重试。如果问题持续，请重启浏览器。'
    });
  }
});

// 监听快捷键命令
chrome.commands.onCommand.addListener(async (command) => {
  console.log('收到快捷键命令:', command);

  try {
    // 确保Service Worker就绪
    await ensureServiceWorkerReady();

    switch (command) {
      case 'save_all_tabs':
        console.log('快捷键保存所有标签页');
        const allTabs = await queryTabsWithRetry();
        await tabManager.saveAllTabs(allTabs);
        break;

      case 'save_current_tab':
        console.log('快捷键保存当前标签页');
        const [activeTab] = await chrome.tabs.query({
          active: true,
          currentWindow: true
        });
        if (activeTab) {
          await tabManager.saveCurrentTab(activeTab);
        } else {
          console.warn('未找到活跃标签页');
        }
        break;

      case '_execute_action':
        console.log('快捷键打开标签管理器');
        await tabManager.openTabManager();
        break;
    }
  } catch (error) {
    console.error('处理快捷键命令失败:', error);

    // 显示错误通知
    chrome.notifications.create({
      type: 'basic',
      iconUrl: '/icons/icon128.png',
      title: 'OneTab Plus - 快捷键操作失败',
      message: '快捷键操作失败，请重试'
    });
  }
});

// 监听右键菜单点击事件
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  console.log('右键菜单点击:', info.menuItemId);

  try {
    if (info.menuItemId === 'open-tab-manager') {
      console.log('点击右键菜单，打开标签管理器');
      await tabManager.openTabManager();
    } else if (info.menuItemId === 'saveCurrentTab' && tab) {
      console.log('点击右键菜单，保存当前标签页');
      await tabManager.saveCurrentTab(tab);
      // 保存后打开标签管理器
      await tabManager.openTabManager(true);
    }
  } catch (error) {
    console.error('处理右键菜单点击失败:', error);
  }
});

// 定义允许的消息类型
const ALLOWED_MESSAGE_TYPES = [
  'SAVE_ALL_TABS',
  'SAVE_CURRENT_TAB',
  'OPEN_TAB',
  'OPEN_TABS',
  'REFRESH_TAB_LIST'
] as const;

// type AllowedMessageType = typeof ALLOWED_MESSAGE_TYPES[number];

// 消息验证函数
function validateMessage(message: any, sender: chrome.runtime.MessageSender): boolean {
  // 检查消息结构
  if (!message || typeof message !== 'object') {
    console.warn('Service Worker: 收到无效消息结构:', message);
    return false;
  }

  // 检查消息类型
  if (!message.type || !ALLOWED_MESSAGE_TYPES.includes(message.type)) {
    console.warn('Service Worker: 收到未知消息类型:', message.type);
    return false;
  }

  // 验证发送者来源
  if (!sender.id || sender.id !== chrome.runtime.id) {
    console.warn('Service Worker: 消息来源验证失败:', sender);
    return false;
  }

  // 验证消息数据结构
  if (message.type !== 'REFRESH_TAB_LIST' && (!message.data || typeof message.data !== 'object')) {
    console.warn('Service Worker: 消息缺少有效数据:', message);
    return false;
  }

  return true;
}

// 安全的错误响应函数
function sendSecureErrorResponse(sendResponse: (response: any) => void, error: Error | string) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  // 过滤敏感信息，只返回安全的错误信息
  const safeErrorMessage = errorMessage.includes('chrome-extension://')
    ? '操作失败，请重试'
    : errorMessage;

  sendResponse({
    success: false,
    error: safeErrorMessage,
    timestamp: Date.now()
  });
}

// 处理消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 验证消息
  if (!validateMessage(message, sender)) {
    sendSecureErrorResponse(sendResponse, '无效的消息格式或来源');
    return false;
  }

  console.log('Service Worker 收到验证通过的消息:', message.type);

  try {
    // 处理保存标签页的消息
    if (message.type === 'SAVE_ALL_TABS') {
      if (!Array.isArray(message.data.tabs)) {
        sendSecureErrorResponse(sendResponse, '标签页数据格式无效');
        return false;
      }

      tabManager.saveAllTabs(message.data.tabs)
        .then(() => sendResponse({ success: true, timestamp: Date.now() }))
        .catch(error => sendSecureErrorResponse(sendResponse, error));
      return true; // 异步响应
    }

    if (message.type === 'SAVE_CURRENT_TAB') {
      if (!message.data.tab || typeof message.data.tab !== 'object') {
        sendSecureErrorResponse(sendResponse, '标签页数据格式无效');
        return false;
      }

      tabManager.saveCurrentTab(message.data.tab)
        .then(() => sendResponse({ success: true, timestamp: Date.now() }))
        .catch(error => sendSecureErrorResponse(sendResponse, error));
      return true; // 异步响应
    }

    if (message.type === 'OPEN_TAB') {
      if (!message.data.url || typeof message.data.url !== 'string') {
        sendSecureErrorResponse(sendResponse, 'URL格式无效');
        return false;
      }

      // 验证URL安全性
      try {
        const url = new URL(message.data.url);
        if (!['http:', 'https:'].includes(url.protocol)) {
          sendSecureErrorResponse(sendResponse, '不支持的URL协议');
          return false;
        }
      } catch {
        sendSecureErrorResponse(sendResponse, 'URL格式无效');
        return false;
      }

      tabManager.openTab(message.data.url)
        .then(() => sendResponse({ success: true, timestamp: Date.now() }))
        .catch(error => sendSecureErrorResponse(sendResponse, error));
      return true; // 异步响应
    }

    if (message.type === 'OPEN_TABS') {
      if (!Array.isArray(message.data.urls)) {
        sendSecureErrorResponse(sendResponse, 'URL列表格式无效');
        return false;
      }

      // 验证所有URL的安全性
      for (const url of message.data.urls) {
        if (typeof url !== 'string') {
          sendSecureErrorResponse(sendResponse, 'URL格式无效');
          return false;
        }

        try {
          const urlObj = new URL(url);
          if (!['http:', 'https:'].includes(urlObj.protocol)) {
            sendSecureErrorResponse(sendResponse, '不支持的URL协议');
            return false;
          }
        } catch {
          sendSecureErrorResponse(sendResponse, 'URL格式无效');
          return false;
        }
      }

      tabManager.openTabs(message.data.urls)
        .then(() => sendResponse({ success: true, timestamp: Date.now() }))
        .catch(error => sendSecureErrorResponse(sendResponse, error));
      return true; // 异步响应
    }

    if (message.type === 'REFRESH_TAB_LIST') {
      // 这个消息类型不需要响应，只是通知
      sendResponse({ success: true, timestamp: Date.now() });
      return false;
    }

    // 如果没有处理消息，返回错误
    sendSecureErrorResponse(sendResponse, '未知的消息类型');
    return false;

  } catch (error) {
    console.error('Service Worker: 处理消息时发生错误:', error);
    sendSecureErrorResponse(sendResponse, '处理消息时发生内部错误');
    return false;
  }
});

// 导出一个空对象，确保这个文件被视为模块
export { };

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试重置到默认视图功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007acc;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .button-demo {
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .button-demo:hover {
            background: #005a9e;
        }
        .icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1>OneTab Plus - 重置到默认视图功能测试</h1>
    
    <div class="test-section success">
        <h2>✅ 功能已实现</h2>
        <p>已成功为插件管理器页面左上角的插件图标添加了点击事件，用户点击后可以回到默认视图。</p>
    </div>

    <div class="test-section">
        <h2>🎯 实现的功能</h2>
        <ul>
            <li><strong>清空搜索查询</strong>：点击图标后会清空当前的搜索内容</li>
            <li><strong>退出重排序模式</strong>：如果当前处于重排序模式，会自动退出</li>
            <li><strong>重置布局模式</strong>：将布局模式重置为默认的双栏布局</li>
            <li><strong>保存设置</strong>：将重置后的设置保存到存储中</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 技术实现</h2>
        <p>修改了 <code>src/components/layout/Header.tsx</code> 文件：</p>
        
        <h3>1. 添加了重置函数</h3>
        <div class="code">
const handleResetToDefaultView = () => {
  // 清空搜索
  setSearchValue('');
  onSearch('');
  
  // 退出重排序模式
  if (settings.reorderMode) {
    dispatch(setReorderMode(false));
  }
  
  // 重置为默认布局模式（双栏）
  if (settings.layoutMode !== 'double') {
    dispatch(setLayoutMode('double'));
    dispatch(saveSettings({
      ...settings,
      layoutMode: 'double',
      reorderMode: false,
    }));
  }
};
        </div>

        <h3>2. 将原来的 SVG 图标包装在按钮中</h3>
        <div class="code">
&lt;button
  onClick={handleResetToDefaultView}
  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
  title="回到默认视图"
  aria-label="回到默认视图"
&gt;
  &lt;svg ... /&gt;
&lt;/button&gt;
        </div>
    </div>

    <div class="test-section">
        <h2>🎨 用户体验</h2>
        <ul>
            <li><strong>视觉反馈</strong>：按钮有悬停效果，提供清晰的交互反馈</li>
            <li><strong>无障碍支持</strong>：添加了 <code>title</code> 和 <code>aria-label</code> 属性</li>
            <li><strong>一键重置</strong>：单次点击即可回到干净的默认状态</li>
            <li><strong>智能判断</strong>：只在需要时才执行重置操作，避免不必要的状态更新</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 测试建议</h2>
        <ol>
            <li>在搜索框中输入一些内容</li>
            <li>切换到三栏或单栏布局模式</li>
            <li>进入重排序模式（如果有的话）</li>
            <li>点击左上角的插件图标</li>
            <li>验证所有状态都被重置为默认值</li>
        </ol>
    </div>

    <div class="test-section warning">
        <h2>⚠️ 注意事项</h2>
        <ul>
            <li>确保在 Chrome 扩展环境中测试功能</li>
            <li>验证重置操作不会影响已保存的标签组数据</li>
            <li>测试在不同主题模式下的视觉效果</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📝 代码变更总结</h2>
        <p>本次修改涉及的文件：</p>
        <ul>
            <li><code>src/components/layout/Header.tsx</code> - 主要实现文件</li>
            <li><code>src/tests/headerResetTest.js</code> - 测试文件（新增）</li>
        </ul>
        
        <p>导入的新依赖：</p>
        <ul>
            <li><code>setLayoutMode</code> - 用于设置布局模式</li>
        </ul>
    </div>

    <script>
        // 简单的交互演示
        function simulateReset() {
            alert('模拟重置到默认视图：\n✓ 清空搜索\n✓ 退出重排序模式\n✓ 重置为双栏布局');
        }
    </script>

    <div style="text-align: center; margin-top: 40px;">
        <button class="button-demo" onclick="simulateReset()">
            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"/>
            </svg>
            点击体验重置功能
        </button>
    </div>
</body>
</html>
